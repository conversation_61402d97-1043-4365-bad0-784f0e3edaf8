﻿

课 程 设 计 任 务 书

**《数据结构》大作业**

1. 目的任务

数据结构大作业是对软件设计的综合训练，包括问题分析、总体设计、用户界面设计、程序设计基本技能和技巧，多人合作，以至一套软件工作规范的训练和科学作风的培养。在数据结构实验中，完成的只是单一而“小”的算法，而本课程设计是对学生的整体编程能力的锻炼。数据结构大作业的目的是训练学生对问题的抽象能力和算法的运用能力。

1. 设计内容

大作业由1~4**名**同学组队完成，可从备选题目中选择一个题目来完成或者自选题目。原则上鼓励自选题目。
**


**【备选题目】**

从下面的备选题目中选择一个或者自拟题目。请发挥你的想象力，选用合适的数据结构与算法。需要理解的是，数据结构大作业重点考核的是数据结构和算法的运用；好的界面呈现可以锦上添花，但并不是决定因素。

1. **简易电子表格** 

设计一个支持基本计算与统计功能和其他一些表格管理/处理功能的计算机软件，使用户可在该软件的支持下，用交互方式进行表格建立、数据输入、数据编辑、统计、计算及其他一些表格操作。

实现功能：

F1. 按表格形式显示表格，并支持用户使用简单的功能键（按键选择式的简单菜单）进行操作；

F2. 建立空白表格，同时在屏幕上显示，使其处于可输入数据状态；

F3. 通过键盘将数据输入在屏幕上的电子表格上，同时要支持基本的数据输入编辑；

F4. 将表格中任意数据块复制到另一块中。复制到目标块时，对目标块中原内容的处理，可选择的方式有：代替、相加、相减、按条件替换；

F5. 支持鼠标操作各项功能；	

F6. 支持汉字显示、输入；

F7. 单元格内可输入公式（表达式），使对应单元格的最终内容为公式的计算结果；

F8. 统计计算：包括合计、求平均、求最大/小；

F9. 统计计算方式：表格按行/列统计计算，表格按块统计计算；

F10. 排序：使任一行/列中的数据按大小（升或降）排列，对字符串型数据，还要可选大小写敏感；

F11. 表格保存：使电子表格存储在磁盘上（磁盘文件），并可随时读入，供继续处理。


1. **导航系统**

   车载导航系统是在城市中驾车的好帮手，不仅能够计算出到达目的地的最优路线，而且可以显示当前位置附近的一些附加信息，比如附近的加油站，餐馆等等。请设计一款导航软件，实现导航系统的核心功能。

- **目标用户**：汽车司机。
- **数据配置**：导航系统中的地图可以抽象为一个图，地点信息和路径信息可以抽象为图中的顶点和边。请设计算法，来产生模拟的地图数据：
  - 随机产生N个二维平面上的顶点（N>=10000），每个顶点对应地图中的一个地点
  - 对于每个地点x，随机建立若干个连边到x附近的地点。每条连边代表一条路径，路径的长度等于边的两个顶点之间的二维坐标距离。
  - 模拟数据必须保证，**产生的图是一个连通图，并且道路之间不应有不合理的交叉**。

- **功能要求**：

F1. 地图显示功能。输入一个坐标，显示距离该坐标最近的100个顶点以及相关联的边。关于如何用vc++画图，或java画图，请在baidu或google上查找相关方法，或者借阅相关的参考书。

F2. 地图缩放功能。提示：地图缩的越小，屏幕上显示的点数就越多，但是太多的点，会看不清楚。所以可以考虑只选择一个单元区域内只显示一个代表的点。

F2. 任意指定两个地点A和B，能计算出A到B的最短路径。并将该最短路径经过的顶点以及连边显示出来。

F3. 模拟车流。请为每条连边增加两个属性：车容量v（饱和状态下这条路所能容纳的汽车的数量）、当前在这条路上的车辆数目n（n>v时为超负荷运作）。假设该路的长度为L，则该路的通行时间可模拟为cLf(n/v)，其中c是常数；f(x)是一个分段函数, x小于等于某个常数时，f(x) = 1，当x大于该常数时，f(x) = 1+ex。每条道路的车容量v和道路的长度L为预先指定的固定参数。请模拟产生汽车在地图中行驶，为简化模型假设在同一条路上每架汽车穿越该路的时间均等于cLf(n/v)。要求实现模拟车流的动态变化，任意时刻，给定一个坐标，能在界面上显示该坐标附近的所有路径，并动态显示各个路径上的车流量的大小（可用不同颜色或其他方法区分车流量的大小级别）。

F4. 综合考虑路况的最短路径。任意时刻，指定两个地点A和B，能根据当前的路况，计算出从A到B最短行车时间，以及相应的最佳路径，并在界面上，将该最短路径经过的顶点以及连边显示出来。

3. **短视频推荐系统**

   近年来，以抖音,快手为代表的移动短视频风靡全球，掀起了新一轮的移动互联网的应用浪潮。这类系统的核心算法之一就是推荐算法。现请你实现一个短视频用户观行为分析和推荐系统，完成以下(不限于)功能:

   F1.通过网页采集或模拟短视频信息，构建不少于十万的视频信息库（可以只包括视频说明信息）。

   F2.模拟用户的点击观看行为，模拟不少于1万的用户。

   F3.针对某特定用户，分析和该用户具有相似兴趣的用户群体。

   F4.针对某特定用户，根据其过去历史的浏览记录，为其推荐相关视频。

   F5.针对某特定视频，根据其过去观看的历史，预测该视频未来被观看的热度变化。

   F6.给视频进行聚类分析，将具有相似观看用户的视频聚成一团。

   F7.给用户进行聚类分析，将具有相似观看兴趣的用户聚成一团。

3. **疫情模拟系统**

   新冠疫情肆虐下，各国采取不同的防疫策略，得到了不同的防疫效果。现就不同种类的防疫策略进行疫情模拟，完成以下（不限于）功能：

   F1. 初始状态设置。

   用图形界面完成，人群总数不小于1000，有家庭、封闭公共场所（模拟商场、影院、教室等）、开放空间、医院、疫情隔离区。

   初始参数包括：Rt传染指数，流动指数，0代病人数量，隔离速度等等。

   F2．不同防疫策略下疫情的发展过程展示。

   防疫策略大致可分为：

1. 完全开放。基本上不加任何管控，也基本不接种疫苗。
1. 疫苗接种+基本不管控。号召人群接种疫苗，但对公众出行活动基本不管控。期望随着疫苗接种率的提升，疫情得到控制。
1. 疫苗接种+公共场所适度管控。在加大疫苗接种的同时，对公众场所和人群出行进行适度管控，比如中小学停课，娱乐场所部分关闭，以及呼吁人群带口罩及消毒。
1. 疫苗接种+公共场所适度管控+零容忍。除了（3）的措施以外，严格执行“零容忍”，只要发现病人，立即隔离，并做好密接者排查和区域大排查。

   要求对以上四种防疫策略，模拟随时间推移的疫情传播情况。除动态图形化模拟外，还应该有动态统计结果，包括：正常人数、患病人数、隔离人数、死亡人数、疫苗接种率。。。。。。

   F3. 做四种防疫策略的对比，用图表等方式进行对比，并得出结论。

3. **培养计划编制**

   本科培养计划编制是一个复杂的问题，要考虑课程之间的先修关系、实践环节、各学期教学任务的平衡、必修课和选修课学分要求、总学分的要求等等。现要求根据实际情况制定你所在专业的教学计划。要求有以下环节：

   F1. 明确专业教学环节，一般包括包括公共基础课、专业必修课、专业选修课、通选课、实践课程、各类实习、毕业设计、军训等。

   F2. 明确各教学环节的具体内容，每门课的学分、课时（有些课程还包括实验课时）、课程之间的选修关系。

   F3. 明确各个教学环节的学分要求，以及总学分要求。

   F4. 设计算法并实施，制订科学可行的培养计划，并用表格方式进行结果展示。

7. **科学文献管理系统**

   科研工作者的日常工作离不开查阅科学文献，并对其中的信息进行分析、筛选、挖掘和管理。请你为科研工作者设计一个管理系统，提高科学文献的管理效率。

- **目标用户**：科研工作者。
- **数据配置**：请通过以下方法下载数据文件dblp.xml.gz.

<a name="ole_link3"></a><a name="ole_link4"></a><http://dblp.uni-trier.de/xml/dblp.xml.gz>

`   `将该数据文件解压后，其中包含一个dblp.xml文件。该文件由科学文献的记录序列组成，记录的格式如下所示：

` `<article mdate="2002-01-03" key="persons/Codd71a">

<author>E. F. Codd</author>

<title>Further Normalization of the Data Base Relational Model.</title>

<journal>IBM Research Report, San Jose, California</journal>

<volume>RJ909</volume>

<month>August</month>

<year>1971</year>

<cdrom>ibmTR/rj909.pdf</cdrom>

<ee>db/labs/ibm/RJ909.html</ee>

</article>

每个记录对应一篇文章，其中包含对作者，题名，发表杂志，卷号，出版时间等的详细说明。请基于该数据，设计能满足后述功能的文献管理系统。

`   `**注：1）dblp.xml的大小超过1G，所以不要直接点击打开该文件。需要通过命令行命令’more’ 或者自行编程查看。**

**2） 编程过程中，不允许使用数据库系统。需要自己建立管理文献数据的数据结构。**

- **功能要求**：

F1. 基本搜索功能。输入作者名，能展示该作者发表的所有论文信息。输入完整的论文的题目，能展示该论文的其他相关信息

F2. 相关搜索。输入作者名，能展示于该作者有合作关系的其他所以作者。

F3. 作者统计功能。输出写文章最多的前100名作者。

F4. 热点分析功能。分析每一年发表的文章中，题目所包含的单词中，出现频率排名前10的关键词。

F5. 部分匹配搜索功能。给定若干个关键字，能快速搜索到题目中包含该关键字的文章信息

F6. 聚团分析。作者之间的合作关系可以看成是一个图，每个作者对应一个顶点，任两个作者之间如果存在合作关系，则在两个顶点之间建立连边。这个图中的每一个完全子图我们称为一个聚团（所谓完全子图指的是该子图的任意顶点都和该子图的其他顶点有连边，完全子图的顶点个数称为该完全子图的阶数），请统计整个图中各阶完全子图的个数。

F7. 可视化显示。通过图形化界面，展示作者之间合作关系图及其相关文章信息。


7. **出租车轨迹分析**

   `   `出租车是城市中主要出行交通工具之一。通过分析城市中的出租车的轨迹，可以获得大量有意义的城市动态信息，包括城市中人群的移动规律、城市交通状况、城市区域的繁华程度、城市区域之间的关联关系、旅游路线推荐等等。

   `   `目前，由于车载GPS设备的广泛使用，以及具有海量信息存储能力的数据中心的推行，采集、存储并实时处理出租车的轨迹已经成为现实。如何对这些轨迹进行有效的管理、分析与挖掘，是一个不仅富有意义而且富有挑战的任务。请根据下面给定的出租车轨迹数据，进行相应的分析以及可视化的展示。

    

- **数据配置**： 

  请通过以下方法下载数据文件01.zip – 014.zip

<http://research.microsoft.com/apps/pubs/?id=152883>

将该数据文件解压后，得到10375个txt文件。文件记录了北京市10,000辆出租车1星期的GPS轨迹数据。记录的格式如下所示：

1,2008-02-02 15:36:08,116.51172,39.92123

1,2008-02-02 15:46:08,116.51135,39.93883

1,2008-02-02 15:46:08,116.51135,39.93883

…

每个记录对应一个时空点，其中包含用户标识Id,时间Time，经度longitude，纬度latitude。请基于该数据，进行出租车轨迹的分析。
**


- **功能要求**：

F1. 出租车轨迹可视化。显示所有或者某部出租车的轨迹。显示的方式可以是在编程绘制的窗口中画出地图并显示出租车的轨迹点；也可以通过调用google地图或者baidu地图的API，在google地图或者baidu地图中显示。请在baidu或google上查找相关方法，或者借阅相关的参考书。

F2. 地图缩放功能。可以对地图进行放大或者缩小，并相应的调整出租车轨迹的展示。

F3. 区域范围查找。统计在某个特定的时间段，用户指定的矩形区域内的出租车的数目。这里的矩形区域可以通过给出矩形的左上角和右下角的经纬度坐标来确定。 

F4. 区域车流密度分析。给定距离参数r,将整个地图划分成网格，每个格子的大小是r\*r。统计分析在不同的时间段，经过所有网格区域内的车流密度的变化。

F5. 区域关联分析1。用户指定两个矩形区域，统计在不同的时间段，往来这两个区域之间的车流量的变化。

F6. 区域关联分析2。用户指定一个矩形区域，统计往来该矩形区域和其他区域的车流量随时间的变化。

F7. 频繁路径分析1。一条路径的频繁度可以定义为这条路径上通行的汽车的总数。根据用户给定的参数k，距离参数x，统计在整个城市中，长度超过x的最为频繁的前k个路径。

F8. 频繁路径分析2。给定两个矩形区域A和B，分析从A到B的最为频繁的前k个通行路径。

F9. 通信时间分析。给定两个矩形区域A和B，分析在不同的时间段，出租车从A到B的通行时间最短的路径，以及相应的通行时间。 

7. **P2P网络电视模拟器**

   `    `网络电视是通过互联网观看实时播放的视频节目的一类应用。常见的网络电视客户端可运行于普通的PC机、移动终端或电视机顶盒。目前比较比较流行的软件包括PPTV、腾讯视频、乐视网络电视等。

   传统的网络电视采用的是客户端-服务器（C/S）的网络架构，所有的网络电视客户端都和服务器建立连接，并向服务器请求视频数据。在这种架构下，服务器的负载和客户端的数目呈线性比例关系，随着用户的增长运营成本急剧增长。为了解决服务器瓶颈问题，一种称为Peer-to-Peer (P2P) 的网络架构被提出并获得广泛推广使用。在基于P2P架构的网络电视系统中，客户端之间建立网络连接，并相互分享下载的视频数据。每个客户端都可以从其他客户端处下载视频数据，并且缓存一定数量的视频数据，同时也可以向其他客户端提供缓存的视频数据。通过这种人人为我，我为人人的方式，发挥客户端之间的网络传输能力，极大降低服务器的负载。

   如何优化P2P网络从而最大程度提升电视客户端的观看质量是一个重要的研究课题。在研究P2P网络优化策略的时候，经常需要模拟P2P网络，并在该网络中观察各种策略对用户体验的影响。请你根据下面的要求，实现一个P2P网络电视模拟器。

- **数据配置**：P2P网络可以抽象为一个图，服务器和每个网络电视客户端都可以抽象为图中的1个顶点，网络连接可以抽象为图中的连边。请设计算法，来产生模拟的数据：
  - 网络中包含N个客户端顶点（N >= 100），1个服务器顶点。每个客户端/服务器随机分配一个二维平面中的坐标(x, y) 用于描述该客户端/服务器的地理位置。
  - 每个客户端或服务器随机挑选网络中的t个客户端或服务器，建立到它们的网络连接
  - 任何两个顶点之间的网络连接的传输速率和节点之间距离成反比，传输速率的取值范围是[20Kbyte/s, 100Kbyte/s]。
  - 服务器每秒产生的视频数据的大小是30Kbyte。视频数据被切分为等大的数据块，每个数据块的大小为1Kbyte。每个数据块都对应一个全局唯一的整数序号，该序号反映数据块产生的时间先后。例如，第一个数据块序号为1，第二个数据块序号为2，依此类推。
  - 每个客户端会定期检测它的邻居节点拥有的数据块序号，并向邻居节点请求自己没有的数据块。邻居节点收到请求后，会将对应的数据块传给该客户端。（本次模拟实验中，可忽略发起请求所需要消耗的时间）
  - 客户端可以连续播放的前提是拥有连续的完整的M个分块 (M>= 5)。也即是客户端能播放数据块i的前提是，该客户端已经下载了数据块i, i+1, i+2,…, i+M-1.
  - 每个客户端可缓存N个数据分块，（N可自由配置）。在客户端上维护一个长度为N的缓存队列，新到达的数据分块会将最老的数据块进行覆盖。 

- **功能要求**：

F1. 网络可视化功能。显示所有客户端的位置和服务器的位置，以及彼此之间的网络连接。关于如何用vc++画图，或java画图，请在baidu或google上查找相关方法，或者借阅相关的参考书。

F2. 网络缩放功能。可以对网络显示区域进行缩放。

F3. 模拟视频数据流。按上述数据配置要求，模拟服务器产生视频块，每个客户端向邻居节点请求数据块。测试客户端播放的流畅度（流畅度可定义为该客户端的连续播放时间占总时间的比例）。（<a name="ole_link2"></a><a name="ole_link1"></a>提示：可基于队列实现“离散事件模拟”，可通过百度、google、电子图书馆等方式获取网络模拟相关知识）

F4. 测试客户端的播放延迟。（客户端的播放延迟 = 数据块在客户端上的播放时间 – 数据块在服务器上的产生时间）。  

F5. 上述数据配置中构建的网络本质上是随机网络。请综合考虑节点之间的传输速率等因素，设计更佳的网络构建方案，使得各个客户端的播放延迟较小，播放流畅度较好。

F6. 模拟节点退出对网络的影响。让网络中R个客户端节点（R< N）停止工作，测试其他客户端的播放延迟和流畅度所受的影响。

F7. 设计邻居更新策略，以降低节点退出对网络的影响。每个节点在检测到邻居节点退出时，按某种既定的策略建立新的网络连接。请给出你的具体策略，并测试该策略的效果。

7. **开放式探索题目**

   可通过下面大数据比赛相关平台，选择自己感兴趣的题目，并下载数据进行实验。最终大作业项目可在这些数据比赛题目的基础上进行扩展，增加用户交互界面，补充一些有趣而使用的功能。请充分发挥你的想象力。

- [https://www.kaggle.com/](https://www.baidu.com/link?url=F3ne8F-IqU9ORi-Dadg2VGowhK0f9QrBokG_fDB06PO3ZX1GO8Ct5JuleJ_TDWeT&wd=&eqid=8f27baac000552cc000000035df054aa)
- <https://tianchi.aliyun.com/home/<USER>
- <https://www.datafountain.cn/>

7. FFT算法设计与迁移

   功能要求：完成1D, 2D  FFT算法设计，并将FFT迁移到GPU/NPU平台，完成并行化扩展，比较CPU和GPU/NPU下性能提升，这是基本要求。

   扩展功能：编写或者调用一个第三方应用，该应用需要使用FFT计算，替换已有FFT，比较编写FFT并行版本，对该应用性能加速效果提升，给出完整性能分析报告。

7. **自选题目**
   **
   `   `根据自己兴趣选定题目，要求：

1. 题目合理，难度适合，能实现对数据结构课程内容应用的目的。
1. 自选题目在3月20日前要发给老师审核确定，再开始做。


1. 时间安排

   大作业时间和进度安排建议如下：

|项  目  内  容|时间|
| :-: | :-: |
|选定题目和任务分配|2025\.3.5～ 2025.3.20|
|需求分析和概要设计|2025\.3.21 ～ 2023.3.31|
|算法设计和编码调试|2025\.4.1 ～ 2025.4.30|
|系统整合和撰写文档|2025\.5.1 ～ 2023.5.15|
|<p>答辩（包括结果演示、</p><p>提交小组和个人文档）</p>|2025年5月下旬|

**5月15日前**提交到微信群的共享文件，每组同学把要提交的所有内容打包，并以“题目+姓名1+姓名2+。。。”作为压缩文件名。答辩时间安排在五月下旬。

1. 设计工作要求
1. 根据问题的工作量和难易程度，由1~4**名**同学组队完成。
1. **五月下旬将安排大作业答辩。答辩以小组为单位进行答辩，论述实现的软件的主要功能、所采用的数据结构和核心算法，以及测试的结果。**
1. 所实现的程序，须满足以下要求：
- 界面友好：图形界面，支持鼠标操作。有合理的中文提示，每个功能可以设立菜单，根据提示，可以完成相关的功能。出现非法输入，会给出异常提示。
- 物理存储：相关数据要求存储在数据文件中，在程序中完成文件的读写操作。
- 逻辑结构：根据问题的要求，采用线性或非线性结构。除少数题目外，均需考虑大数据量问题。
1. **每位同学**还需要针对自己所承担的部分工作，提交报告文档**。**该文档是评分的重要依据之一，请认真对待。该文档须包括如下内容：
- 需求分析： (陈述要解决的问题，要实现的功能),
- 详细设计：包括设计算法流程图、算法分析、使用的数据结构 **（要求详细论证）**；
- 软件测试：包括测试数据和测试结果记录 
- 总结： 设计过程中遇到的问题及解决方法；尚未解决的问题及考虑应对的策略；收获和心得；
- 参考文献：在提交的设计报告中**必须要有参考文献！必须按学校规定的模板来写！**可以是课本以及与课程设计相关的工具书、论文、网页等。要求在报告中按规定格式详细列举所用参考文献。

`  `**具体格式请参照《课程设计报告书》模板。**

1. **在提交的设计报告中，要能体现出合理的分工和充足的工作量。要求每人都要交设计报告，系统整体设计部分可以相同或相似，但自己做的部分内容要详细叙述。**
1. **以小组作为单元，提交如下内容**：   
- 源代码。要求**注释清晰**，编写规范，模块化。编程语言: C++/Java/phython/…
- 可运行的文件。
- 答辩PPT。



1. 成绩评定

1\. 考核办法：在学生完成设计、调试后，组织验收。以小组为单位进行答辩。

2\. 成绩评定：

- 答辩成绩占60%。以小组为考核对象，重点考查数据结构的运用、问题应答、项目整体完成情况。
- 书面大作业报告占40%。以个人为考核对象，重点考查个人负责的部分的完成情况。

1. 参考文献
1  Clifford A.Shaffer.数据结构与算法分析（C++版） （第三版）.  电子工业出版社. 2013.
1  严蔚敏，吴伟民. 数据结构(C语言版)（第二版）. 人民邮电出版社.2016. 
1  殷人昆等. 数据结构（用面向对象方法与C++语言描述）第二版. 清华大学出版社. 2007.


